{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5m1.75 1c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m-3.5 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1M8.5 13c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m1.75 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1M12 13c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m1.75 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m1.75-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9.5 12c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m4.25-2c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1m-3.5 0c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1m0 4c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1M22 11.25v1.5c0 .41-.34.75-.75.75s-.75-.34-.75-.75h-1.54c-.15 1.37-.69 2.63-1.52 3.65l1.09 1.09.01-.01c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-1.06 1.06c-.29.29-.77.29-1.06 0-.29-.29-.29-.76-.01-1.05l-1.09-1.09a7.015 7.015 0 0 1-3.64 1.51v1.54h.01c.41 0 .75.34.75.75s-.34.75-.75.75h-1.5c-.41 0-.75-.34-.75-.75s.33-.74.74-.75v-1.55c-1.37-.15-2.62-.69-3.63-1.51l-1.09 1.09.01.01c.29.29.29.77 0 1.06-.29.29-.77.29-1.06 0L4.4 18.54c-.29-.29-.29-.77 0-1.06.29-.29.76-.29 1.05-.01l1.09-1.09c-.82-1.02-1.36-2.26-1.5-3.63H3.5c0 .41-.34.75-.75.75S2 13.16 2 12.75v-1.5c0-.41.34-.75.75-.75s.75.34.75.75h1.54c.15-1.37.69-2.61 1.5-3.63L5.45 6.53c-.29.28-.76.28-1.05-.01-.29-.29-.29-.77 0-1.06L5.46 4.4c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-.01.01L7.6 6.56c1.02-.82 2.26-1.36 3.63-1.51V3.5c-.41-.01-.74-.34-.74-.75.01-.41.35-.75.76-.75h1.5c.41 0 .75.34.75.75s-.34.75-.75.75h-.01v1.54c1.37.14 2.62.69 3.64 1.51l1.09-1.09c-.29-.29-.28-.76.01-1.05.29-.29.77-.29 1.06 0l1.06 1.06c.29.29.29.77 0 1.06s-.77.29-1.06 0l-.01-.01-1.09 1.08c.82 1.02 1.37 2.27 1.52 3.65h1.54c0-.41.34-.75.75-.75s.75.34.75.75M17 12c0-2.76-2.24-5-5-5s-5 2.24-5 5 2.24 5 5 5 5-2.24 5-5m-5-1c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1m3.5 0c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1m-1.75 3c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1\"\n}, \"1\")], 'CoronavirusTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["c:/Users/<USER>/Desktop/KQICK/kqick-admin/node_modules/@mui/icons-material/esm/CoronavirusTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5m1.75 1c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1m-3.5 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1M8.5 13c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m1.75 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1M12 13c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m1.75 3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1m1.75-3c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M9.5 12c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1m4.25-2c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1m-3.5 0c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1m0 4c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1M22 11.25v1.5c0 .41-.34.75-.75.75s-.75-.34-.75-.75h-1.54c-.15 1.37-.69 2.63-1.52 3.65l1.09 1.09.01-.01c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-1.06 1.06c-.29.29-.77.29-1.06 0-.29-.29-.29-.76-.01-1.05l-1.09-1.09a7.015 7.015 0 0 1-3.64 1.51v1.54h.01c.41 0 .75.34.75.75s-.34.75-.75.75h-1.5c-.41 0-.75-.34-.75-.75s.33-.74.74-.75v-1.55c-1.37-.15-2.62-.69-3.63-1.51l-1.09 1.09.01.01c.29.29.29.77 0 1.06-.29.29-.77.29-1.06 0L4.4 18.54c-.29-.29-.29-.77 0-1.06.29-.29.76-.29 1.05-.01l1.09-1.09c-.82-1.02-1.36-2.26-1.5-3.63H3.5c0 .41-.34.75-.75.75S2 13.16 2 12.75v-1.5c0-.41.34-.75.75-.75s.75.34.75.75h1.54c.15-1.37.69-2.61 1.5-3.63L5.45 6.53c-.29.28-.76.28-1.05-.01-.29-.29-.29-.77 0-1.06L5.46 4.4c.29-.29.77-.29 1.06 0 .29.29.29.77 0 1.06l-.01.01L7.6 6.56c1.02-.82 2.26-1.36 3.63-1.51V3.5c-.41-.01-.74-.34-.74-.75.01-.41.35-.75.76-.75h1.5c.41 0 .75.34.75.75s-.34.75-.75.75h-.01v1.54c1.37.14 2.62.69 3.64 1.51l1.09-1.09c-.29-.29-.28-.76.01-1.05.29-.29.77-.29 1.06 0l1.06 1.06c.29.29.29.77 0 1.06s-.77.29-1.06 0l-.01-.01-1.09 1.08c.82 1.02 1.37 2.27 1.52 3.65h1.54c0-.41.34-.75.75-.75s.75.34.75.75M17 12c0-2.76-2.24-5-5-5s-5 2.24-5 5 2.24 5 5 5 5-2.24 5-5m-5-1c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1m3.5 0c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1m-1.75 3c-.55 0-1 .45-1 1s.45 1 1 1 1-.45 1-1-.45-1-1-1\"\n}, \"1\")], 'CoronavirusTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,obAAob;EACvbC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}